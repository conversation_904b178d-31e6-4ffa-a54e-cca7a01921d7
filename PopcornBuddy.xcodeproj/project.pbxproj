// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		B0D93E722DEAA566006AEAC8 /* Lottie in Frameworks */ = {isa = PBXBuildFile; productRef = B0D93E712DEAA566006AEAC8 /* Lottie */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		B020F6C12DEA896F002C32FC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B020F6A62DEA896E002C32FC /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B020F6AD2DEA896E002C32FC;
			remoteInfo = PopcornBuddy;
		};
		B020F6CB2DEA896F002C32FC /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B020F6A62DEA896E002C32FC /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B020F6AD2DEA896E002C32FC;
			remoteInfo = PopcornBuddy;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		B020F6AE2DEA896E002C32FC /* PopcornBuddy.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PopcornBuddy.app; sourceTree = BUILT_PRODUCTS_DIR; };
		B020F6C02DEA896F002C32FC /* PopcornBuddyTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PopcornBuddyTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		B020F6CA2DEA896F002C32FC /* PopcornBuddyUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PopcornBuddyUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		B020F6B02DEA896E002C32FC /* PopcornBuddy */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PopcornBuddy;
			sourceTree = "<group>";
		};
		B020F6C32DEA896F002C32FC /* PopcornBuddyTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PopcornBuddyTests;
			sourceTree = "<group>";
		};
		B020F6CD2DEA896F002C32FC /* PopcornBuddyUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PopcornBuddyUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		B020F6AB2DEA896E002C32FC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B0D93E722DEAA566006AEAC8 /* Lottie in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B020F6BD2DEA896F002C32FC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B020F6C72DEA896F002C32FC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		B020F6A52DEA896E002C32FC = {
			isa = PBXGroup;
			children = (
				B020F6B02DEA896E002C32FC /* PopcornBuddy */,
				B020F6C32DEA896F002C32FC /* PopcornBuddyTests */,
				B020F6CD2DEA896F002C32FC /* PopcornBuddyUITests */,
				B020F6AF2DEA896E002C32FC /* Products */,
			);
			sourceTree = "<group>";
		};
		B020F6AF2DEA896E002C32FC /* Products */ = {
			isa = PBXGroup;
			children = (
				B020F6AE2DEA896E002C32FC /* PopcornBuddy.app */,
				B020F6C02DEA896F002C32FC /* PopcornBuddyTests.xctest */,
				B020F6CA2DEA896F002C32FC /* PopcornBuddyUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B020F6AD2DEA896E002C32FC /* PopcornBuddy */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B020F6D42DEA896F002C32FC /* Build configuration list for PBXNativeTarget "PopcornBuddy" */;
			buildPhases = (
				B020F6AA2DEA896E002C32FC /* Sources */,
				B020F6AB2DEA896E002C32FC /* Frameworks */,
				B020F6AC2DEA896E002C32FC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				B020F6B02DEA896E002C32FC /* PopcornBuddy */,
			);
			name = PopcornBuddy;
			packageProductDependencies = (
				B0D93E712DEAA566006AEAC8 /* Lottie */,
			);
			productName = PopcornBuddy;
			productReference = B020F6AE2DEA896E002C32FC /* PopcornBuddy.app */;
			productType = "com.apple.product-type.application";
		};
		B020F6BF2DEA896F002C32FC /* PopcornBuddyTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B020F6D72DEA896F002C32FC /* Build configuration list for PBXNativeTarget "PopcornBuddyTests" */;
			buildPhases = (
				B020F6BC2DEA896F002C32FC /* Sources */,
				B020F6BD2DEA896F002C32FC /* Frameworks */,
				B020F6BE2DEA896F002C32FC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B020F6C22DEA896F002C32FC /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				B020F6C32DEA896F002C32FC /* PopcornBuddyTests */,
			);
			name = PopcornBuddyTests;
			packageProductDependencies = (
			);
			productName = PopcornBuddyTests;
			productReference = B020F6C02DEA896F002C32FC /* PopcornBuddyTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		B020F6C92DEA896F002C32FC /* PopcornBuddyUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B020F6DA2DEA896F002C32FC /* Build configuration list for PBXNativeTarget "PopcornBuddyUITests" */;
			buildPhases = (
				B020F6C62DEA896F002C32FC /* Sources */,
				B020F6C72DEA896F002C32FC /* Frameworks */,
				B020F6C82DEA896F002C32FC /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B020F6CC2DEA896F002C32FC /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				B020F6CD2DEA896F002C32FC /* PopcornBuddyUITests */,
			);
			name = PopcornBuddyUITests;
			packageProductDependencies = (
			);
			productName = PopcornBuddyUITests;
			productReference = B020F6CA2DEA896F002C32FC /* PopcornBuddyUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B020F6A62DEA896E002C32FC /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					B020F6AD2DEA896E002C32FC = {
						CreatedOnToolsVersion = 16.4;
					};
					B020F6BF2DEA896F002C32FC = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = B020F6AD2DEA896E002C32FC;
					};
					B020F6C92DEA896F002C32FC = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = B020F6AD2DEA896E002C32FC;
					};
				};
			};
			buildConfigurationList = B020F6A92DEA896E002C32FC /* Build configuration list for PBXProject "PopcornBuddy" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = B020F6A52DEA896E002C32FC;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				B0D93E702DEAA566006AEAC8 /* XCRemoteSwiftPackageReference "lottie-ios" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = B020F6AF2DEA896E002C32FC /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B020F6AD2DEA896E002C32FC /* PopcornBuddy */,
				B020F6BF2DEA896F002C32FC /* PopcornBuddyTests */,
				B020F6C92DEA896F002C32FC /* PopcornBuddyUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B020F6AC2DEA896E002C32FC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B020F6BE2DEA896F002C32FC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B020F6C82DEA896F002C32FC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B020F6AA2DEA896E002C32FC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B020F6BC2DEA896F002C32FC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B020F6C62DEA896F002C32FC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		B020F6C22DEA896F002C32FC /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B020F6AD2DEA896E002C32FC /* PopcornBuddy */;
			targetProxy = B020F6C12DEA896F002C32FC /* PBXContainerItemProxy */;
		};
		B020F6CC2DEA896F002C32FC /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B020F6AD2DEA896E002C32FC /* PopcornBuddy */;
			targetProxy = B020F6CB2DEA896F002C32FC /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		B020F6D22DEA896F002C32FC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		B020F6D32DEA896F002C32FC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		B020F6D52DEA896F002C32FC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = bingqi.PopcornBuddy;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		B020F6D62DEA896F002C32FC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = bingqi.PopcornBuddy;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		B020F6D82DEA896F002C32FC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = bingqi.PopcornBuddyTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PopcornBuddy.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/PopcornBuddy";
			};
			name = Debug;
		};
		B020F6D92DEA896F002C32FC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = bingqi.PopcornBuddyTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PopcornBuddy.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/PopcornBuddy";
			};
			name = Release;
		};
		B020F6DB2DEA896F002C32FC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = bingqi.PopcornBuddyUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = PopcornBuddy;
			};
			name = Debug;
		};
		B020F6DC2DEA896F002C32FC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = bingqi.PopcornBuddyUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = PopcornBuddy;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B020F6A92DEA896E002C32FC /* Build configuration list for PBXProject "PopcornBuddy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B020F6D22DEA896F002C32FC /* Debug */,
				B020F6D32DEA896F002C32FC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B020F6D42DEA896F002C32FC /* Build configuration list for PBXNativeTarget "PopcornBuddy" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B020F6D52DEA896F002C32FC /* Debug */,
				B020F6D62DEA896F002C32FC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B020F6D72DEA896F002C32FC /* Build configuration list for PBXNativeTarget "PopcornBuddyTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B020F6D82DEA896F002C32FC /* Debug */,
				B020F6D92DEA896F002C32FC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B020F6DA2DEA896F002C32FC /* Build configuration list for PBXNativeTarget "PopcornBuddyUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B020F6DB2DEA896F002C32FC /* Debug */,
				B020F6DC2DEA896F002C32FC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		B0D93E702DEAA566006AEAC8 /* XCRemoteSwiftPackageReference "lottie-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/airbnb/lottie-ios";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 4.5.2;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		B0D93E712DEAA566006AEAC8 /* Lottie */ = {
			isa = XCSwiftPackageProductDependency;
			package = B0D93E702DEAA566006AEAC8 /* XCRemoteSwiftPackageReference "lottie-ios" */;
			productName = Lottie;
		};

/* End XCSwiftPackageProductDependency section */
	};
	rootObject = B020F6A62DEA896E002C32FC /* Project object */;
}
