import SwiftUI
import CoreData
import CloudKit

@main
struct PopcornBuddyApp: App {
    let persistenceController = PersistenceController.shared
    @State private var showingAPISetup = false

    var body: some Scene {
        WindowGroup {
            Group {
                if !ConfigurationManager.shared.hasValidAPIKeys() {
                    APIKeySetupView()
                } else {
                    ContentView()
                }
            }
            .environment(\.managedObjectContext, persistenceController.container.viewContext)
            .preferredColorScheme(.dark) // Retro theme works best in dark mode
            .onAppear {
                setupNotifications()
            }
        }
    }

    private func setupNotifications() {
        Task {
            await NotificationManager.shared.requestPermission()
            NotificationManager.shared.setupNotificationCategories()
        }
    }
}

// MARK: - Core Data Stack
class PersistenceController {
    static let shared = PersistenceController()
    
    static var preview: PersistenceController = {
        let result = PersistenceController(inMemory: true)
        let viewContext = result.container.viewContext
        
        // Add sample data for previews
        let sampleMovie = Movie(context: viewContext)
        sampleMovie.id = 550
        sampleMovie.title = "Fight Club"
        sampleMovie.overview = "A ticking-time-bomb insomniac and a slippery soap salesman channel primal male aggression into a shocking new form of therapy."
        sampleMovie.releaseDate = Date()
        sampleMovie.posterPath = "/pB8BM7pdSp6B6Ih7QZ4DrQ3PmJK.jpg"
        sampleMovie.backdropPath = "/fCayJrkfRaCRCTh8GqN30f8oyQF.jpg"
        sampleMovie.voteAverage = 8.4
        sampleMovie.runtime = 139
        sampleMovie.isWatched = true
        sampleMovie.userRating = 9.0
        
        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
        }
        return result
    }()
    
    let container: NSPersistentCloudKitContainer

    init(inMemory: Bool = false) {
        container = NSPersistentCloudKitContainer(name: "DataModel")

        if inMemory {
            container.persistentStoreDescriptions.first!.url = URL(fileURLWithPath: "/dev/null")
        } else {
            // Configure CloudKit
            guard let description = container.persistentStoreDescriptions.first else {
                fatalError("Failed to retrieve a persistent store description.")
            }

            // Enable CloudKit sync
            description.setOption(true as NSNumber, forKey: NSPersistentHistoryTrackingKey)
            description.setOption(true as NSNumber, forKey: NSPersistentStoreRemoteChangeNotificationPostOptionKey)
//            description.setOption("iCloud.bingqi.PopcornBuddy" as NSString, forKey: NSPersistentCloudKitContainerOptionsKey)
        }

        container.loadPersistentStores { _, error in
            if let error = error as NSError? {
                fatalError("Unresolved error \(error), \(error.userInfo)")
            }
        }

        container.viewContext.automaticallyMergesChangesFromParent = true

        // Configure for CloudKit
        do {
            try container.viewContext.setQueryGenerationFrom(.current)
        } catch {
            fatalError("Failed to pin viewContext to the current generation: \(error)")
        }
    }
}

// MARK: - Core Data Extensions
extension PersistenceController {
    func save() {
        let context = container.viewContext

        if context.hasChanges {
            do {
                try context.save()
            } catch {
                let nsError = error as NSError
                fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
            }
        }
    }

    // MARK: - CloudKit Sync Management
    func checkCloudKitAccountStatus() async -> CKAccountStatus {
        return await withCheckedContinuation { continuation in
            CKContainer.default().accountStatus { status, error in
                if let error = error {
                    print("CloudKit account status error: \(error)")
                }
                continuation.resume(returning: status)
            }
        }
    }

    func requestCloudKitPermissions() async -> Bool {
        return await withCheckedContinuation { continuation in
            CKContainer.default().requestApplicationPermission(.userDiscoverability) { status, error in
                if let error = error {
                    print("CloudKit permission error: \(error)")
                    continuation.resume(returning: false)
                } else {
                    continuation.resume(returning: status == .granted)
                }
            }
        }
    }

    func syncWithCloudKit() async {
        do {
            // Trigger a sync by saving the context
            if container.viewContext.hasChanges {
                try container.viewContext.save()
            }

            // Wait for CloudKit sync to complete
            try await container.persistentStoreCoordinator.perform {
                // This will trigger CloudKit sync
            }
        } catch {
            print("CloudKit sync error: \(error)")
        }
    }

    func resetCloudKitData() async throws {
        // This should be used carefully - it will reset all CloudKit data
        let stores = container.persistentStoreCoordinator.persistentStores

        for store in stores {
            try container.persistentStoreCoordinator.remove(store)
        }

        // Reload the stores
        container.loadPersistentStores { _, error in
            if let error = error {
                print("Error reloading stores: \(error)")
            }
        }
    }
}
