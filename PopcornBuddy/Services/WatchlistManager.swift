import Foundation
import CoreData
import Combine

// MARK: - Watchlist Manager
class WatchlistManager: ObservableObject {
    static let shared = WatchlistManager()
    
    @Published var movies: [Movie] = []
    @Published var tvShows: [TVShow] = []
    
    private let persistenceController = PersistenceController.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        fetchWatchlistItems()
    }
    
    // MARK: - Fetch Methods
    func fetchWatchlistItems() {
        let context = persistenceController.container.viewContext
        
        // Fetch movies
        let movieRequest: NSFetchRequest<Movie> = Movie.fetchRequest()
        movieRequest.sortDescriptors = [NSSortDescriptor(key: "watchedDate", ascending: false)]
        
        // Fetch TV shows
        let tvShowRequest: NSFetchRequest<TVShow> = TVShow.fetchRequest()
        tvShowRequest.sortDescriptors = [NSSortDescriptor(key: "firstAirDate", ascending: false)]
        
        do {
            movies = try context.fetch(movieRequest)
            tvShows = try context.fetch(tvShowRequest)
        } catch {
            print("Error fetching watchlist items: \(error)")
        }
    }
    
    // MARK: - Movie Methods
    func addMovieToWatchlist(id: Int, title: String, overview: String, posterPath: String?, backdropPath: String?, rating: Double, year: String?, runtime: Int?) {
        let context = persistenceController.container.viewContext
        
        // Check if movie already exists
        let request: NSFetchRequest<Movie> = Movie.fetchRequest()
        request.predicate = NSPredicate(format: "id == %d", id)
        
        do {
            let existingMovies = try context.fetch(request)
            if !existingMovies.isEmpty {
                print("Movie already in watchlist")
                return
            }
        } catch {
            print("Error checking existing movie: \(error)")
            return
        }
        
        // Create new movie
        let movie = Movie(context: context)
        movie.id = Int32(id)
        movie.title = title
        movie.overview = overview
        movie.posterPath = posterPath
        movie.backdropPath = backdropPath
        movie.voteAverage = rating
        movie.runtime = Int32(runtime ?? 0)
        movie.isWatched = false
        movie.userRating = 0.0
        
        if let year = year {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy"
            movie.releaseDate = formatter.date(from: year)
        }
        
        saveContext()
        fetchWatchlistItems()
    }
    
    func removeMovieFromWatchlist(id: Int) {
        let context = persistenceController.container.viewContext
        
        let request: NSFetchRequest<Movie> = Movie.fetchRequest()
        request.predicate = NSPredicate(format: "id == %d", id)
        
        do {
            let movies = try context.fetch(request)
            for movie in movies {
                context.delete(movie)
            }
            saveContext()
            fetchWatchlistItems()
        } catch {
            print("Error removing movie from watchlist: \(error)")
        }
    }
    
    func toggleMovieWatchedStatus(id: Int) {
        let context = persistenceController.container.viewContext
        
        let request: NSFetchRequest<Movie> = Movie.fetchRequest()
        request.predicate = NSPredicate(format: "id == %d", id)
        
        do {
            let movies = try context.fetch(request)
            if let movie = movies.first {
                movie.isWatched.toggle()
                if movie.isWatched {
                    movie.watchedDate = Date()
                } else {
                    movie.watchedDate = nil
                }
                saveContext()
                fetchWatchlistItems()
            }
        } catch {
            print("Error toggling movie watched status: \(error)")
        }
    }
    
    func rateMovie(id: Int, rating: Double) {
        let context = persistenceController.container.viewContext
        
        let request: NSFetchRequest<Movie> = Movie.fetchRequest()
        request.predicate = NSPredicate(format: "id == %d", id)
        
        do {
            let movies = try context.fetch(request)
            if let movie = movies.first {
                movie.userRating = rating
                saveContext()
                fetchWatchlistItems()
            }
        } catch {
            print("Error rating movie: \(error)")
        }
    }
    
    // MARK: - TV Show Methods
    func addTVShowToWatchlist(id: Int, title: String, overview: String, posterPath: String?, backdropPath: String?, rating: Double, year: String?) {
        let context = persistenceController.container.viewContext
        
        // Check if TV show already exists
        let request: NSFetchRequest<TVShow> = TVShow.fetchRequest()
        request.predicate = NSPredicate(format: "id == %d", id)
        
        do {
            let existingShows = try context.fetch(request)
            if !existingShows.isEmpty {
                print("TV show already in watchlist")
                return
            }
        } catch {
            print("Error checking existing TV show: \(error)")
            return
        }
        
        // Create new TV show
        let tvShow = TVShow(context: context)
        tvShow.id = Int32(id)
        tvShow.name = title
        tvShow.overview = overview
        tvShow.posterPath = posterPath
        tvShow.backdropPath = backdropPath
        tvShow.voteAverage = rating
        tvShow.userRating = 0.0
        
        if let year = year {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy"
            tvShow.firstAirDate = formatter.date(from: year)
        }
        
        saveContext()
        fetchWatchlistItems()
    }
    
    func removeTVShowFromWatchlist(id: Int) {
        let context = persistenceController.container.viewContext
        
        let request: NSFetchRequest<TVShow> = TVShow.fetchRequest()
        request.predicate = NSPredicate(format: "id == %d", id)
        
        do {
            let tvShows = try context.fetch(request)
            for tvShow in tvShows {
                context.delete(tvShow)
            }
            saveContext()
            fetchWatchlistItems()
        } catch {
            print("Error removing TV show from watchlist: \(error)")
        }
    }
    
    func rateTVShow(id: Int, rating: Double) {
        let context = persistenceController.container.viewContext
        
        let request: NSFetchRequest<TVShow> = TVShow.fetchRequest()
        request.predicate = NSPredicate(format: "id == %d", id)
        
        do {
            let tvShows = try context.fetch(request)
            if let tvShow = tvShows.first {
                tvShow.userRating = rating
                saveContext()
                fetchWatchlistItems()
            }
        } catch {
            print("Error rating TV show: \(error)")
        }
    }
    
    // MARK: - Helper Methods
    func isInWatchlist(id: Int, type: MediaType) -> Bool {
        switch type {
        case .movie:
            return movies.contains { $0.id == id }
        case .tvShow:
            return tvShows.contains { $0.id == id }
        }
    }
    
    func getUserRating(id: Int, type: MediaType) -> Double {
        switch type {
        case .movie:
            return movies.first { $0.id == id }?.userRating ?? 0.0
        case .tvShow:
            return tvShows.first { $0.id == id }?.userRating ?? 0.0
        }
    }
    
    func isWatched(id: Int, type: MediaType) -> Bool {
        switch type {
        case .movie:
            return movies.first { $0.id == id }?.isWatched ?? false
        case .tvShow:
            // For TV shows, we could implement episode-based watching logic
            return false
        }
    }
    
    private func saveContext() {
        let context = persistenceController.container.viewContext
        
        if context.hasChanges {
            do {
                try context.save()
            } catch {
                print("Error saving context: \(error)")
            }
        }
    }
}

// MARK: - Media Type Enum
enum MediaType {
    case movie
    case tvShow
}
