import SwiftUI

struct RetroTVHomeView: View {
    @State private var selectedChannel: RetroChannel? = nil
    @State private var showingSettings = false
    @State private var showingProfile = false
    @State private var tvIsOn = true
    @State private var staticAnimation = false
    @State private var showingWatchlist = false
    @State private var showingAIRecommendation = false

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Retro background
                retroBackground

                VStack(spacing: 0) {
                    // TV Section (top 1/3)
                    tvSection(geometry: geometry)
                        .frame(height: geometry.size.height / 3)

                    // Workspace Section (bottom 2/3)
                    workspaceSection(geometry: geometry)
                        .frame(height: geometry.size.height * 2/3)
                }
            }
        }
        .navigationBarHidden(true)
        .preferredColorScheme(.dark)
        .sheet(item: $selectedChannel) { channel in
            RetroChannelListView(channel: channel)
        }
        .sheet(isPresented: $showingSettings) {
            RetroSettingsView()
        }
        .sheet(isPresented: $showingProfile) {
            RetroProfileView()
        }
        .sheet(isPresented: $showingWatchlist) {
            RetroWatchlistView()
        }
        .sheet(isPresented: $showingAIRecommendation) {
            RetroAIRecommendationView()
        }
    }
    
    // MARK: - Background
    private var retroBackground: some View {
        LinearGradient(
            colors: [
                Color(red: 0.2, green: 0.15, blue: 0.1),
                Color(red: 0.15, green: 0.1, blue: 0.05),
                Color(red: 0.1, green: 0.05, blue: 0.02)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - TV Section
    private func tvSection(geometry: GeometryProxy) -> some View {
        HStack(spacing: 0) {
            // TV with screen
            ZStack {
                // TV Frame
                RoundedRectangle(cornerRadius: 25)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color(red: 0.4, green: 0.3, blue: 0.2),
                                Color(red: 0.3, green: 0.2, blue: 0.1),
                                Color(red: 0.2, green: 0.15, blue: 0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: geometry.size.width * 0.7, height: geometry.size.height * 0.25)
                    .shadow(color: .black.opacity(0.5), radius: 15, x: 0, y: 8)

                // TV Screen
                RoundedRectangle(cornerRadius: 15)
                    .fill(Color.black)
                    .frame(width: geometry.size.width * 0.6, height: geometry.size.height * 0.2)
                    .overlay(
                        Group {
                            if tvIsOn {
                                tvScreenContent(geometry: geometry)
                            } else {
                                tvOffScreen
                            }
                        }
                    )
            }

            // Control Knobs
            VStack(spacing: 20) {
                // Settings Knob
                controlKnob(
                    icon: "gearshape.fill",
                    color: .orange,
                    action: { showingSettings = true }
                )
            }
            .padding(.leading, 20)

            Spacer()
        }
        .padding(.horizontal)
    }
    
    // MARK: - TV Screen Content
    private func tvScreenContent(geometry: GeometryProxy) -> some View {
        ZStack {
            // Screen glow effect
            RoundedRectangle(cornerRadius: 15)
                .fill(
                    RadialGradient(
                        colors: [
                            Color.white.opacity(0.1),
                            Color.clear
                        ],
                        center: .center,
                        startRadius: 0,
                        endRadius: 200
                    )
                )
            
            // Channel Selection Grid
            VStack(spacing: 30) {
                // Title
                Text("POPCORN BUDDY")
                    .font(.custom("Courier", size: 24))
                    .fontWeight(.bold)
                    .foregroundColor(.orange)
                    .shadow(color: .orange.opacity(0.5), radius: 5)
                
                // Channel Buttons
                VStack(spacing: 20) {
                    channelButton(
                        title: "MOVIES",
                        subtitle: "Channel 1",
                        icon: "film.fill",
                        channel: .movies
                    )
                    
                    channelButton(
                        title: "TV SHOWS",
                        subtitle: "Channel 2", 
                        icon: "tv.fill",
                        channel: .tvShows
                    )
                    
                    channelButton(
                        title: "BLIND BOX",
                        subtitle: "Channel 3",
                        icon: "questionmark.square.fill",
                        channel: .blindBox
                    )
                }
            }
            .padding()
        }
    }
    
    // MARK: - Channel Button
    private func channelButton(title: String, subtitle: String, icon: String, channel: RetroChannel) -> some View {
        Button(action: {
            selectedChannel = channel
        }) {
            HStack(spacing: 15) {
                Image(systemName: icon)
                    .font(.title)
                    .foregroundColor(.orange)
                    .frame(width: 40)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.custom("Courier", size: 18))
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text(subtitle)
                        .font(.custom("Courier", size: 12))
                        .foregroundColor(.gray)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.title2)
                    .foregroundColor(.orange)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 15)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.white.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(RetroButtonStyle())
    }
    
    // MARK: - TV Off Screen
    private var tvOffScreen: some View {
        Rectangle()
            .fill(Color.black)
            .overlay(
                Text("📺")
                    .font(.system(size: 60))
                    .opacity(0.3)
            )
    }
    
    // MARK: - Control Knob
    private func controlKnob(icon: String, color: Color, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            ZStack {
                // Outer ring
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [
                                Color(red: 0.4, green: 0.3, blue: 0.2),
                                Color(red: 0.2, green: 0.15, blue: 0.1)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 50, height: 50)
                    .shadow(color: .black.opacity(0.3), radius: 5, x: 2, y: 2)

                // Inner circle
                Circle()
                    .fill(color.opacity(0.8))
                    .frame(width: 35, height: 35)
                    .overlay(
                        Circle()
                            .stroke(Color.white.opacity(0.3), lineWidth: 1)
                    )

                // Icon
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(.white)
            }
        }
        .buttonStyle(RetroButtonStyle())
    }

    // MARK: - Workspace Section
    private func workspaceSection(geometry: GeometryProxy) -> some View {
        ZStack {
            // Workspace background
            Rectangle()
                .fill(
                    LinearGradient(
                        colors: [
                            Color(red: 0.15, green: 0.1, blue: 0.05),
                            Color(red: 0.1, green: 0.08, blue: 0.04)
                        ],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )

            VStack(spacing: 20) {
                Spacer()

                HStack(spacing: 25) {
                    // Remote Control
                    remoteControl

                    Spacer()

                    // Toy Box (Watchlist)
                    toyBox

                    Spacer()

                    // Printer (AI Recommendations)
                    printer
                }
                .padding(.horizontal, 20)

                Spacer()
            }
        }
    }

    // MARK: - Remote Control
    private var remoteControl: some View {
        VStack(spacing: 15) {
            // Remote body
            RoundedRectangle(cornerRadius: 15)
                .fill(
                    LinearGradient(
                        colors: [
                            Color(red: 0.2, green: 0.2, blue: 0.2),
                            Color(red: 0.1, green: 0.1, blue: 0.1)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 70, height: 100)
                .overlay(
                    VStack(spacing: 8) {
                        // Power button
                        Circle()
                            .fill(Color.red.opacity(0.8))
                            .frame(width: 20, height: 20)
                            .overlay(
                                Image(systemName: "power")
                                    .font(.system(size: 10, weight: .bold))
                                    .foregroundColor(.white)
                            )

                        // Channel buttons
                        VStack(spacing: 4) {
                            HStack(spacing: 8) {
                                remoteButton("1")
                                remoteButton("2")
                                remoteButton("3")
                            }
                        }

                        // Navigation pad
                        Circle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 30, height: 30)
                            .overlay(
                                Image(systemName: "plus")
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(.white)
                            )
                    }
                    .padding()
                )
                .shadow(color: .black.opacity(0.5), radius: 8, x: 2, y: 4)

            Text("REMOTE")
                .font(.custom("Courier", size: 10))
                .fontWeight(.bold)
                .foregroundColor(.orange)
        }
    }

    // MARK: - Toy Box (Watchlist)
    private var toyBox: some View {
        Button(action: { showingWatchlist = true }) {
            VStack(spacing: 15) {
                // Box
                RoundedRectangle(cornerRadius: 12)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color(red: 0.6, green: 0.4, blue: 0.2),
                                Color(red: 0.4, green: 0.3, blue: 0.15)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 60)
                    .overlay(
                        VStack(spacing: 5) {
                            // Lid
                            Rectangle()
                                .fill(Color(red: 0.5, green: 0.35, blue: 0.18))
                                .frame(width: 80, height: 8)
                                .cornerRadius(4)

                            // Contents (movie icons)
                            HStack(spacing: 3) {
                                Image(systemName: "film")
                                    .font(.system(size: 8))
                                    .foregroundColor(.orange)
                                Image(systemName: "tv")
                                    .font(.system(size: 8))
                                    .foregroundColor(.blue)
                                Image(systemName: "star")
                                    .font(.system(size: 8))
                                    .foregroundColor(.yellow)
                            }
                        }
                        .offset(y: -5)
                    )
                    .shadow(color: .black.opacity(0.5), radius: 8, x: 2, y: 4)

                Text("TOY BOX")
                    .font(.custom("Courier", size: 10))
                    .fontWeight(.bold)
                    .foregroundColor(.orange)
            }
        }
        .buttonStyle(RetroButtonStyle())
    }

    // MARK: - Printer (AI Recommendations)
    private var printer: some View {
        Button(action: { showingAIRecommendation = true }) {
            VStack(spacing: 15) {
                // Printer body
                RoundedRectangle(cornerRadius: 8)
                    .fill(
                        LinearGradient(
                            colors: [
                                Color(red: 0.8, green: 0.8, blue: 0.8),
                                Color(red: 0.6, green: 0.6, blue: 0.6)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 75, height: 55)
                    .overlay(
                        VStack(spacing: 3) {
                            // Paper tray
                            Rectangle()
                                .fill(Color.white)
                                .frame(width: 70, height: 8)
                                .cornerRadius(2)

                            // Paper coming out
                            Rectangle()
                                .fill(Color.white)
                                .frame(width: 60, height: 25)
                                .cornerRadius(2)
                                .overlay(
                                    VStack(spacing: 1) {
                                        Rectangle()
                                            .fill(Color.black.opacity(0.3))
                                            .frame(width: 50, height: 2)
                                        Rectangle()
                                            .fill(Color.black.opacity(0.3))
                                            .frame(width: 45, height: 2)
                                        Rectangle()
                                            .fill(Color.black.opacity(0.3))
                                            .frame(width: 40, height: 2)
                                    }
                                )
                                .offset(y: 8)

                            // AI indicator
                            Circle()
                                .fill(Color.green)
                                .frame(width: 6, height: 6)
                                .offset(x: 25, y: -15)
                        }
                    )
                    .shadow(color: .black.opacity(0.5), radius: 8, x: 2, y: 4)

                Text("PRINTER")
                    .font(.custom("Courier", size: 10))
                    .fontWeight(.bold)
                    .foregroundColor(.orange)
            }
        }
        .buttonStyle(RetroButtonStyle())
    }

    // MARK: - Remote Button Helper
    private func remoteButton(_ text: String) -> some View {
        Circle()
            .fill(Color.gray.opacity(0.5))
            .frame(width: 15, height: 15)
            .overlay(
                Text(text)
                    .font(.system(size: 8, weight: .bold))
                    .foregroundColor(.white)
            )
    }
}

// MARK: - Retro Channel Enum
enum RetroChannel: String, CaseIterable, Identifiable {
    case movies = "movies"
    case tvShows = "tvShows"
    case blindBox = "blindBox"
    
    var id: String { rawValue }
    
    var title: String {
        switch self {
        case .movies: return "Movies"
        case .tvShows: return "TV Shows"
        case .blindBox: return "Blind Box"
        }
    }
}

// MARK: - Retro Button Style
struct RetroButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .brightness(configuration.isPressed ? -0.1 : 0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

#Preview {
    RetroTVHomeView()
}
