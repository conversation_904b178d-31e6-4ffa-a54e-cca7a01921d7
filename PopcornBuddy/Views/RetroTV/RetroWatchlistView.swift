import SwiftUI

struct RetroWatchlistView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTab = 0
    
    var body: some View {
        NavigationView {
            ZStack {
                // Retro background
                LinearGradient(
                    colors: [
                        Color(red: 0.1, green: 0.05, blue: 0.02),
                        Color(red: 0.05, green: 0.03, blue: 0.01)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Header
                    VStack(spacing: 10) {
                        Text("TOY BOX")
                            .font(.custom("Courier", size: 24))
                            .fontWeight(.bold)
                            .foregroundColor(.orange)
                        
                        Text("YOUR COLLECTION")
                            .font(.custom("Courier", size: 12))
                            .foregroundColor(.gray)
                    }
                    .padding()
                    
                    // Tab Selector
                    HStack(spacing: 0) {
                        tabButton(title: "WATCHLIST", index: 0)
                        tabButton(title: "WATCHED", index: 1)
                        tabButton(title: "FAVORITES", index: 2)
                    }
                    .padding(.horizontal)
                    
                    // Content
                    TabView(selection: $selectedTab) {
                        watchlistContent
                            .tag(0)
                        
                        watchedContent
                            .tag(1)
                        
                        favoritesContent
                            .tag(2)
                    }
                    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                    
                    // Close Button
                    Button(action: { dismiss() }) {
                        Text("CLOSE TOY BOX")
                            .font(.custom("Courier", size: 14))
                            .fontWeight(.bold)
                            .foregroundColor(.black)
                            .padding(.horizontal, 25)
                            .padding(.vertical, 10)
                            .background(Color.orange)
                            .cornerRadius(0)
                    }
                    .padding()
                }
            }
            .navigationBarHidden(true)
        }
    }
    
    // MARK: - Tab Button
    private func tabButton(title: String, index: Int) -> some View {
        Button(action: { selectedTab = index }) {
            Text(title)
                .font(.custom("Courier", size: 12))
                .fontWeight(.bold)
                .foregroundColor(selectedTab == index ? .black : .orange)
                .padding(.horizontal, 20)
                .padding(.vertical, 8)
                .background(selectedTab == index ? Color.orange : Color.clear)
                .overlay(
                    Rectangle()
                        .stroke(Color.orange, lineWidth: 1)
                )
        }
    }
    
    // MARK: - Content Views
    private var watchlistContent: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("🎬 Movies & TV Shows")
                    .font(.custom("Courier", size: 16))
                    .foregroundColor(.white)
                
                Text("Your watchlist items will appear here")
                    .font(.custom("Courier", size: 12))
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                
                // Placeholder items
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 15) {
                    ForEach(0..<6, id: \.self) { _ in
                        toyBoxItem()
                    }
                }
                .padding()
            }
            .padding()
        }
    }
    
    private var watchedContent: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("✅ Completed")
                    .font(.custom("Courier", size: 16))
                    .foregroundColor(.white)
                
                Text("Your watched items will appear here")
                    .font(.custom("Courier", size: 12))
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                
                // Placeholder items
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 15) {
                    ForEach(0..<9, id: \.self) { _ in
                        toyBoxItem()
                    }
                }
                .padding()
            }
            .padding()
        }
    }
    
    private var favoritesContent: some View {
        ScrollView {
            VStack(spacing: 20) {
                Text("⭐ Favorites")
                    .font(.custom("Courier", size: 16))
                    .foregroundColor(.white)
                
                Text("Your favorite items will appear here")
                    .font(.custom("Courier", size: 12))
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                
                // Placeholder items
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 15) {
                    ForEach(0..<3, id: \.self) { _ in
                        toyBoxItem()
                    }
                }
                .padding()
            }
            .padding()
        }
    }
    
    // MARK: - Toy Box Item
    private func toyBoxItem() -> some View {
        RoundedRectangle(cornerRadius: 8)
            .fill(Color.white.opacity(0.1))
            .frame(width: 80, height: 120)
            .overlay(
                VStack(spacing: 5) {
                    Image(systemName: ["film", "tv", "star"].randomElement() ?? "film")
                        .font(.system(size: 20))
                        .foregroundColor(.orange)
                    
                    Text("Item")
                        .font(.custom("Courier", size: 8))
                        .foregroundColor(.white)
                }
            )
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.orange.opacity(0.3), lineWidth: 1)
            )
    }
}

#Preview {
    RetroWatchlistView()
}
