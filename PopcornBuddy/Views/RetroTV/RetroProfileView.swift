import SwiftUI

struct RetroProfileView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                // Retro background
                LinearGradient(
                    colors: [
                        Color(red: 0.1, green: 0.05, blue: 0.02),
                        Color(red: 0.05, green: 0.03, blue: 0.01)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 30) {
                    // Header
                    VStack(spacing: 10) {
                        Text("USER PROFILE")
                            .font(.custom("Courier", size: 24))
                            .fontWeight(.bold)
                            .foregroundColor(.orange)
                        
                        Text("RETRO TV SYSTEM")
                            .font(.custom("Courier", size: 12))
                            .foregroundColor(.gray)
                    }
                    
                    // Profile Avatar
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [Color.orange, Color.red],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 120, height: 120)
                        .overlay(
                            Text("👤")
                                .font(.system(size: 60))
                        )
                        .shadow(color: .black.opacity(0.5), radius: 10, x: 0, y: 5)
                    
                    // User Info
                    VStack(spacing: 15) {
                        Text("Movie Enthusiast")
                            .font(.custom("Courier", size: 20))
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                        
                        Text("Member since 2024")
                            .font(.custom("Courier", size: 14))
                            .foregroundColor(.gray)
                    }
                    
                    // Stats Grid
                    HStack(spacing: 40) {
                        statCard(title: "WATCHED", value: "127", icon: "eye.fill")
                        statCard(title: "RATED", value: "89", icon: "star.fill")
                        statCard(title: "WATCHLIST", value: "23", icon: "bookmark.fill")
                    }
                    
                    Spacer()
                    
                    // Close Button
                    Button(action: { dismiss() }) {
                        Text("CLOSE")
                            .font(.custom("Courier", size: 16))
                            .fontWeight(.bold)
                            .foregroundColor(.black)
                            .padding(.horizontal, 30)
                            .padding(.vertical, 12)
                            .background(Color.orange)
                            .cornerRadius(0)
                    }
                    .padding(.bottom, 30)
                }
                .padding()
            }
            .navigationBarHidden(true)
        }
    }
    
    // MARK: - Stat Card
    private func statCard(title: String, value: String, icon: String) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 20, weight: .bold))
                .foregroundColor(.orange)
            
            Text(value)
                .font(.custom("Courier", size: 18))
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Text(title)
                .font(.custom("Courier", size: 10))
                .foregroundColor(.gray)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

#Preview {
    RetroProfileView()
}
