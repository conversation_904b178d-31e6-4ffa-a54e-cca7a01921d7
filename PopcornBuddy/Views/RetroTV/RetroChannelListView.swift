import SwiftUI

struct RetroChannelListView: View {
    let channel: RetroChannel
    @Environment(\.dismiss) private var dismiss
    @StateObject private var sampleData = SampleDataManager.shared
    @State private var selectedItem: MediaDisplayItem? = nil
    
    var body: some View {
        NavigationView {
            ZStack {
                // Retro background
                retroBackground
                
                VStack(spacing: 0) {
                    // Channel Header
                    channelHeader
                    
                    // Channel Content
                    ScrollView {
                        LazyVStack(spacing: 12) {
                            ForEach(channelContent, id: \.id) { item in
                                RetroChannelRow(item: item) {
                                    selectedItem = item
                                }
                                .padding(.horizontal)
                            }
                        }
                        .padding(.vertical)
                    }
                }
            }
            .navigationBarHidden(true)
        }
        .sheet(item: $selectedItem) { item in
            RetroDetailView(mediaItem: item.toMediaItem())
        }
    }
    
    // MARK: - Background
    private var retroBackground: some View {
        LinearGradient(
            colors: [
                Color(red: 0.1, green: 0.05, blue: 0.02),
                Color(red: 0.05, green: 0.02, blue: 0.01)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - Channel Header
    private var channelHeader: some View {
        VStack(spacing: 10) {
            HStack {
                Button(action: { dismiss() }) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                        Text("BACK")
                    }
                    .font(.custom("Courier", size: 16))
                    .fontWeight(.bold)
                    .foregroundColor(.orange)
                }
                
                Spacer()
                
                Text("CH \(channelNumber)")
                    .font(.custom("Courier", size: 16))
                    .fontWeight(.bold)
                    .foregroundColor(.gray)
            }
            .padding(.horizontal)
            
            // Channel Title
            Text(channel.title.uppercased())
                .font(.custom("Courier", size: 28))
                .fontWeight(.bold)
                .foregroundColor(.orange)
                .shadow(color: .orange.opacity(0.5), radius: 3)
            
            // Subtitle
            Text(channelSubtitle)
                .font(.custom("Courier", size: 14))
                .foregroundColor(.gray)
        }
        .padding(.vertical, 20)
        .background(
            Rectangle()
                .fill(Color.black.opacity(0.3))
                .blur(radius: 1)
        )
    }
    
    // MARK: - Channel Content
    private var channelContent: [MediaDisplayItem] {
        switch channel {
        case .movies:
            return sampleData.recentlyAdded + sampleData.trendingContent
        case .tvShows:
            return sampleData.trendingContent + sampleData.recentlyAdded
        case .blindBox:
            return sampleData.trendingContent.shuffled()
        }
    }
    
    private var channelNumber: String {
        switch channel {
        case .movies: return "001"
        case .tvShows: return "002"
        case .blindBox: return "003"
        }
    }
    
    private var channelSubtitle: String {
        switch channel {
        case .movies: return "Now Playing • Coming Soon"
        case .tvShows: return "Popular Series • New Episodes"
        case .blindBox: return "Mystery Recommendations • Surprise Me!"
        }
    }
}

// MARK: - Retro Channel Row
struct RetroChannelRow: View {
    let item: MediaDisplayItem
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 15) {
                // Channel number indicator
                VStack {
                    Rectangle()
                        .fill(Color.orange)
                        .frame(width: 4, height: 60)
                    
                    Spacer()
                }
                
                // Poster thumbnail
                AsyncImage(url: item.posterURL) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .overlay(
                            Image(systemName: "photo")
                                .foregroundColor(.gray)
                        )
                }
                .frame(width: 60, height: 90)
                .clipShape(RoundedRectangle(cornerRadius: 8))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                )
                
                // Content info
                VStack(alignment: .leading, spacing: 8) {
                    // Title
                    Text(item.title)
                        .font(.custom("Courier", size: 18))
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)

                    // Year
                    Text(item.year)
                        .font(.custom("Courier", size: 12))
                        .foregroundColor(.gray)

                    // Genres
                    if !item.genres.isEmpty {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 6) {
                                ForEach(item.genres.prefix(3), id: \.self) { genre in
                                    Text(genre.uppercased())
                                        .font(.custom("Courier", size: 8))
                                        .fontWeight(.bold)
                                        .foregroundColor(.black)
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 2)
                                        .background(
                                            RoundedRectangle(cornerRadius: 3)
                                                .fill(Color.orange)
                                        )
                                }
                            }
                            .padding(.horizontal, 1)
                        }
                    }
                }
                
                Spacer()
                
                // Arrow indicator
                Image(systemName: "chevron.right")
                    .font(.title2)
                    .foregroundColor(.orange)
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.white.opacity(0.05))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(
                                LinearGradient(
                                    colors: [
                                        Color.orange.opacity(0.3),
                                        Color.orange.opacity(0.1)
                                    ],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                ),
                                lineWidth: 1
                            )
                    )
            )
        }
        .buttonStyle(RetroButtonStyle())
    }
}

#Preview {
    RetroChannelListView(channel: .movies)
}
