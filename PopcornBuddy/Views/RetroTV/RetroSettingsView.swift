import SwiftUI

struct RetroSettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var showingProfileEdit = false
    @State private var showingAbout = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Retro background
                retroBackground
                
                VStack(spacing: 0) {
                    // TV Control Panel Header
                    controlPanelHeader
                    
                    ScrollView {
                        VStack(spacing: 20) {
                            // Profile Section
                            profileSection
                            
                            // Settings Sections
                            settingsSection
                            
                            // About Section
                            aboutSection
                        }
                        .padding()
                    }
                }
            }
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingProfileEdit) {
            RetroProfileEditView()
        }
        .sheet(isPresented: $showingAbout) {
            RetroAboutView()
        }
    }
    
    // MARK: - Background
    private var retroBackground: some View {
        LinearGradient(
            colors: [
                Color(red: 0.2, green: 0.15, blue: 0.1),
                Color(red: 0.15, green: 0.1, blue: 0.05),
                Color(red: 0.1, green: 0.05, blue: 0.02)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .ignoresSafeArea()
    }
    
    // MARK: - Control Panel Header
    private var controlPanelHeader: some View {
        VStack(spacing: 10) {
            HStack {
                Button(action: { dismiss() }) {
                    HStack(spacing: 8) {
                        Image(systemName: "chevron.left")
                        Text("BACK")
                    }
                    .font(.custom("Courier", size: 16))
                    .fontWeight(.bold)
                    .foregroundColor(.orange)
                }
                
                Spacer()
                
                Text("CONTROL PANEL")
                    .font(.custom("Courier", size: 20))
                    .fontWeight(.bold)
                    .foregroundColor(.orange)
                
                Spacer()
                
                // Power indicator
                Circle()
                    .fill(Color.green)
                    .frame(width: 12, height: 12)
                    .overlay(
                        Circle()
                            .stroke(Color.white, lineWidth: 1)
                    )
            }
            .padding(.horizontal)
            
            Rectangle()
                .fill(Color.orange.opacity(0.3))
                .frame(height: 1)
                .padding(.horizontal)
        }
        .padding(.vertical, 15)
        .background(Color.black.opacity(0.3))
    }
    
    // MARK: - Profile Section
    private var profileSection: some View {
        VStack(spacing: 15) {
            Text("USER PROFILE")
                .font(.custom("Courier", size: 18))
                .fontWeight(.bold)
                .foregroundColor(.orange)
            
            VStack(spacing: 15) {
                // Profile Avatar
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [Color.orange, Color.red],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 80, height: 80)
                    .overlay(
                        Text("👤")
                            .font(.system(size: 40))
                    )
                
                // User Info
                VStack(spacing: 5) {
                    Text("Movie Enthusiast")
                        .font(.custom("Courier", size: 16))
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text("Member since 2024")
                        .font(.custom("Courier", size: 12))
                        .foregroundColor(.gray)
                }
                
                // Stats
                HStack(spacing: 30) {
                    statItem(title: "WATCHED", value: "127")
                    statItem(title: "RATED", value: "89")
                    statItem(title: "WATCHLIST", value: "23")
                }
                
                // Edit Profile Button
                Button(action: { showingProfileEdit = true }) {
                    Text("EDIT PROFILE")
                        .font(.custom("Courier", size: 14))
                        .fontWeight(.bold)
                        .foregroundColor(.black)
                        .padding(.horizontal, 20)
                        .padding(.vertical, 8)
                        .background(Color.orange)
                        .cornerRadius(0)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.white.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - Settings Section
    private var settingsSection: some View {
        VStack(spacing: 15) {
            Text("SYSTEM SETTINGS")
                .font(.custom("Courier", size: 18))
                .fontWeight(.bold)
                .foregroundColor(.orange)
            
            VStack(spacing: 0) {
                settingRow(
                    icon: "icloud",
                    title: "iCloud Sync",
                    subtitle: "Sync across devices",
                    action: {}
                )
                
                settingRow(
                    icon: "bell",
                    title: "Notifications",
                    subtitle: "Daily recommendations",
                    action: {}
                )
                
                settingRow(
                    icon: "moon",
                    title: "Display Mode",
                    subtitle: "Always retro theme",
                    action: {}
                )
                
                settingRow(
                    icon: "brain.head.profile",
                    title: "AI Preferences",
                    subtitle: "Customize recommendations",
                    action: {}
                )
                
                settingRow(
                    icon: "key",
                    title: "API Settings",
                    subtitle: "Configure services",
                    action: {}
                )
            }
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.white.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - About Section
    private var aboutSection: some View {
        VStack(spacing: 15) {
            Text("INFORMATION")
                .font(.custom("Courier", size: 18))
                .fontWeight(.bold)
                .foregroundColor(.orange)
            
            VStack(spacing: 0) {
                settingRow(
                    icon: "questionmark.circle",
                    title: "Help & Support",
                    subtitle: "Get assistance",
                    action: {}
                )
                
                settingRow(
                    icon: "info.circle",
                    title: "About PopcornBuddy",
                    subtitle: "Version 2.0 Retro",
                    action: { showingAbout = true }
                )
                
                settingRow(
                    icon: "star",
                    title: "Rate App",
                    subtitle: "Share your feedback",
                    action: {}
                )
            }
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.white.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                    )
            )
        }
    }
    
    // MARK: - Helper Views
    private func statItem(title: String, value: String) -> some View {
        VStack(spacing: 2) {
            Text(value)
                .font(.custom("Courier", size: 18))
                .fontWeight(.bold)
                .foregroundColor(.orange)
            
            Text(title)
                .font(.custom("Courier", size: 10))
                .foregroundColor(.gray)
        }
    }
    
    private func settingRow(icon: String, title: String, subtitle: String, action: @escaping () -> Void) -> some View {
        Button(action: action) {
            HStack(spacing: 15) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(.orange)
                    .frame(width: 30)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.custom("Courier", size: 14))
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                    
                    Text(subtitle)
                        .font(.custom("Courier", size: 11))
                        .foregroundColor(.gray)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.orange)
            }
            .padding()
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Profile Edit View
struct RetroProfileEditView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        Text("Profile Edit - Coming Soon")
            .font(.custom("Courier", size: 18))
            .foregroundColor(.orange)
    }
}

// MARK: - About View
struct RetroAboutView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack {
            Text("PopcornBuddy Retro")
                .font(.custom("Courier", size: 24))
                .fontWeight(.bold)
                .foregroundColor(.orange)
            
            Text("Version 2.0")
                .font(.custom("Courier", size: 16))
                .foregroundColor(.gray)
        }
    }
}

#Preview {
    RetroSettingsView()
}
