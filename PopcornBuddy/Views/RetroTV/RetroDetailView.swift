import SwiftUI

struct RetroDetailView: View {
    let mediaItem: MediaItem
    @Environment(\.dismiss) private var dismiss
    @StateObject private var watchlistManager = WatchlistManager.shared
    @State private var userRating: Double = 0
    @State private var showingRatingSheet = false
    @State private var aiHighlights: String = ""
    @State private var isLoadingHighlights = false
    @State private var isInWatchlist = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Newspaper background
                newspaperBackground
                
                ScrollView {
                    VStack(spacing: 0) {
                        // Newspaper Header
                        newspaperHeader
                        
                        // Main Content
                        VStack(spacing: 20) {
                            // Title Section
                            titleSection
                            
                            // Image and Info Section
                            imageAndInfoSection
                            
                            // AI Highlights Section
                            aiHighlightsSection
                            
                            // Description Section
                            descriptionSection
                            
                            // Rating Section
                            ratingSection
                            
                            // Action Buttons
                            actionButtons
                        }
                        .padding()
                    }
                }
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            setupInitialState()
            generateAIHighlights()
        }
        .sheet(isPresented: $showingRatingSheet) {
            RetroRatingSheetView(currentRating: userRating) { rating in
                userRating = rating
            }
        }
    }
    
    // MARK: - Background
    private var newspaperBackground: some View {
        Color(red: 0.95, green: 0.92, blue: 0.85)
            .ignoresSafeArea()
            .overlay(
                // Newspaper texture
                Image(systemName: "doc.text")
                    .font(.system(size: 200))
                    .foregroundColor(.black.opacity(0.02))
                    .rotationEffect(.degrees(-15))
            )
    }
    
    // MARK: - Newspaper Header
    private var newspaperHeader: some View {
        VStack(spacing: 5) {
            HStack {
                Button(action: { dismiss() }) {
                    HStack(spacing: 5) {
                        Image(systemName: "chevron.left")
                        Text("BACK")
                    }
                    .font(.custom("Times New Roman", size: 16))
                    .fontWeight(.bold)
                    .foregroundColor(.black)
                }
                
                Spacer()
                
                Text("EST. 2024")
                    .font(.custom("Times New Roman", size: 12))
                    .foregroundColor(.gray)
            }
            .padding(.horizontal)
            
            Text("THE POPCORN TIMES")
                .font(.custom("Times New Roman", size: 32))
                .fontWeight(.bold)
                .foregroundColor(.black)
            
            Rectangle()
                .fill(Color.black)
                .frame(height: 2)
                .padding(.horizontal)
            
            Text("Entertainment News & Reviews")
                .font(.custom("Times New Roman", size: 14))
                .italic()
                .foregroundColor(.gray)
        }
        .padding(.vertical, 15)
        .background(Color.white.opacity(0.8))
    }
    
    // MARK: - Title Section
    private var titleSection: some View {
        VStack(spacing: 8) {
            Text(mediaItem.title.uppercased())
                .font(.custom("Times New Roman", size: 28))
                .fontWeight(.bold)
                .foregroundColor(.black)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
            
            if let year = mediaItem.year {
                Text("Released \(year)")
                    .font(.custom("Times New Roman", size: 16))
                    .italic()
                    .foregroundColor(.gray)
            }
        }
    }
    
    // MARK: - Image and Info Section
    private var imageAndInfoSection: some View {
        HStack(alignment: .top, spacing: 20) {
            // Poster Image
            AsyncImage(url: mediaItem.posterURL) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fit)
            } placeholder: {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .overlay(
                        Image(systemName: "photo")
                            .font(.title)
                            .foregroundColor(.gray)
                    )
            }
            .frame(width: 120, height: 180)
            .clipShape(RoundedRectangle(cornerRadius: 8))
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.black, lineWidth: 2)
            )
            
            // Info Column
            VStack(alignment: .leading, spacing: 12) {
                // Rating Box
                VStack(spacing: 4) {
                    Text("CRITIC SCORE")
                        .font(.custom("Times New Roman", size: 12))
                        .fontWeight(.bold)
                        .foregroundColor(.black)
                    
                    Text(String(format: "%.1f/10", mediaItem.rating))
                        .font(.custom("Times New Roman", size: 24))
                        .fontWeight(.bold)
                        .foregroundColor(.black)
                }
                .padding()
                .background(
                    Rectangle()
                        .stroke(Color.black, lineWidth: 2)
                        .background(Color.white.opacity(0.8))
                )
                
                // Runtime (if available)
                if let runtime = mediaItem.runtime {
                    VStack(alignment: .leading, spacing: 2) {
                        Text("RUNTIME")
                            .font(.custom("Times New Roman", size: 12))
                            .fontWeight(.bold)
                            .foregroundColor(.black)
                        
                        Text("\(runtime) minutes")
                            .font(.custom("Times New Roman", size: 14))
                            .foregroundColor(.gray)
                    }
                }
                
                Spacer()
            }
        }
    }
    
    // MARK: - AI Highlights Section
    private var aiHighlightsSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                Text("HIGHLIGHTS")
                    .font(.custom("Times New Roman", size: 18))
                    .fontWeight(.bold)
                    .foregroundColor(.black)
                
                Spacer()
                
                if isLoadingHighlights {
                    ProgressView()
                        .scaleEffect(0.8)
                }
            }
            
            Rectangle()
                .fill(Color.black)
                .frame(height: 1)
            
            if !aiHighlights.isEmpty {
                Text(aiHighlights)
                    .font(.custom("Times New Roman", size: 14))
                    .foregroundColor(.black)
                    .lineLimit(nil)
                    .multilineTextAlignment(.leading)
            } else if !isLoadingHighlights {
                Text("Generating highlights...")
                    .font(.custom("Times New Roman", size: 14))
                    .italic()
                    .foregroundColor(.gray)
            }
        }
    }
    
    // MARK: - Description Section
    private var descriptionSection: some View {
        VStack(alignment: .leading, spacing: 10) {
            Text("SYNOPSIS")
                .font(.custom("Times New Roman", size: 18))
                .fontWeight(.bold)
                .foregroundColor(.black)
            
            Rectangle()
                .fill(Color.black)
                .frame(height: 1)
            
            Text(mediaItem.overview)
                .font(.custom("Times New Roman", size: 14))
                .foregroundColor(.black)
                .lineLimit(nil)
                .multilineTextAlignment(.leading)
        }
    }
    
    // MARK: - Rating Section
    private var ratingSection: some View {
        VStack(spacing: 15) {
            Text("READER RATING")
                .font(.custom("Times New Roman", size: 18))
                .fontWeight(.bold)
                .foregroundColor(.black)
            
            Rectangle()
                .fill(Color.black)
                .frame(height: 1)
            
            HStack(spacing: 30) {
                // Thumbs Up
                Button(action: { 
                    userRating = userRating == 10 ? 0 : 10
                }) {
                    VStack(spacing: 8) {
                        Image(systemName: userRating >= 6 ? "hand.thumbsup.fill" : "hand.thumbsup")
                            .font(.system(size: 40))
                            .foregroundColor(userRating >= 6 ? .green : .gray)
                        
                        Text("GOOD")
                            .font(.custom("Times New Roman", size: 12))
                            .fontWeight(.bold)
                            .foregroundColor(.black)
                    }
                }
                
                // Thumbs Down
                Button(action: { 
                    userRating = userRating == 1 ? 0 : 1
                }) {
                    VStack(spacing: 8) {
                        Image(systemName: userRating <= 5 && userRating > 0 ? "hand.thumbsdown.fill" : "hand.thumbsdown")
                            .font(.system(size: 40))
                            .foregroundColor(userRating <= 5 && userRating > 0 ? .red : .gray)
                        
                        Text("BAD")
                            .font(.custom("Times New Roman", size: 12))
                            .fontWeight(.bold)
                            .foregroundColor(.black)
                    }
                }
            }
            .padding()
            .background(
                Rectangle()
                    .stroke(Color.black, lineWidth: 1)
                    .background(Color.white.opacity(0.5))
            )
        }
    }
    
    // MARK: - Action Buttons
    private var actionButtons: some View {
        HStack(spacing: 20) {
            Button(action: toggleWatchlist) {
                HStack {
                    Image(systemName: isInWatchlist ? "bookmark.fill" : "bookmark")
                    Text(isInWatchlist ? "REMOVE" : "ADD TO LIST")
                }
                .font(.custom("Times New Roman", size: 14))
                .fontWeight(.bold)
                .foregroundColor(.white)
                .padding()
                .background(Color.black)
                .cornerRadius(0)
            }
            
            Button(action: { showingRatingSheet = true }) {
                HStack {
                    Image(systemName: "star")
                    Text("DETAILED RATING")
                }
                .font(.custom("Times New Roman", size: 14))
                .fontWeight(.bold)
                .foregroundColor(.black)
                .padding()
                .background(
                    Rectangle()
                        .stroke(Color.black, lineWidth: 2)
                        .background(Color.white.opacity(0.8))
                )
            }
        }
        .padding(.top, 20)
    }
    
    // MARK: - Helper Methods
    private func setupInitialState() {
        // Check if item is in watchlist
        // This would need to be implemented based on your watchlist logic
    }
    
    private func generateAIHighlights() {
        isLoadingHighlights = true

        Task {
            do {
                let highlights = try await OpenAIService.shared.generateMediaHighlights(
                    title: mediaItem.title,
                    overview: mediaItem.overview,
                    year: mediaItem.year,
                    rating: mediaItem.rating
                )

                await MainActor.run {
                    aiHighlights = highlights
                    isLoadingHighlights = false
                }
            } catch {
                await MainActor.run {
                    aiHighlights = "• Critically acclaimed performance by lead actors\n• Stunning cinematography and visual effects\n• Compelling storyline with unexpected twists\n• Award-winning soundtrack and score"
                    isLoadingHighlights = false
                }
            }
        }
    }
    
    private func toggleWatchlist() {
        isInWatchlist.toggle()
        // Implement watchlist logic here
    }
}

#Preview {
    RetroDetailView(
        mediaItem: MediaItem(
            id: 1,
            title: "Sample Movie",
            overview: "This is a sample movie overview that describes the plot and main characters of the movie in great detail.",
            posterURL: nil,
            backdropURL: nil,
            rating: 8.5,
            year: "2024",
            runtime: 120
        )
    )
}
