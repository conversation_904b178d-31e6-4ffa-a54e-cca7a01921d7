import SwiftUI

struct RetroRatingSheetView: View {
    let currentRating: Double
    let onRatingSelected: (Double) -> Void
    
    @State private var selectedRating: Double = 0
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                // Newspaper background
                Color(red: 0.95, green: 0.92, blue: 0.85)
                    .ignoresSafeArea()
                
                VStack(spacing: 30) {
                    // Header
                    VStack(spacing: 10) {
                        Text("THE POPCORN TIMES")
                            .font(.custom("Times New Roman", size: 24))
                            .fontWeight(.bold)
                            .foregroundColor(.black)
                        
                        Rectangle()
                            .fill(Color.black)
                            .frame(height: 2)
                            .frame(maxWidth: 200)
                        
                        Text("READER REVIEW")
                            .font(.custom("Times New Roman", size: 18))
                            .fontWeight(.bold)
                            .foregroundColor(.black)
                        
                        Text("Rate this entertainment")
                            .font(.custom("Times New Roman", size: 14))
                            .italic()
                            .foregroundColor(.gray)
                    }
                    .padding(.top, 20)
                    
                    // Rating Section
                    VStack(spacing: 20) {
                        Text("SELECT YOUR RATING")
                            .font(.custom("Times New Roman", size: 16))
                            .fontWeight(.bold)
                            .foregroundColor(.black)
                        
                        // Star Rating (1-10)
                        VStack(spacing: 15) {
                            HStack(spacing: 8) {
                                ForEach(1...5, id: \.self) { index in
                                    Button(action: {
                                        withAnimation(.easeInOut(duration: 0.2)) {
                                            selectedRating = Double(index * 2)
                                        }
                                    }) {
                                        Image(systemName: Double(index * 2) <= selectedRating ? "star.fill" : "star")
                                            .font(.title)
                                            .foregroundColor(Double(index * 2) <= selectedRating ? .orange : .gray)
                                            .scaleEffect(Double(index * 2) <= selectedRating ? 1.2 : 1.0)
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                }
                            }
                            
                            HStack(spacing: 8) {
                                ForEach(6...10, id: \.self) { index in
                                    Button(action: {
                                        withAnimation(.easeInOut(duration: 0.2)) {
                                            selectedRating = Double(index)
                                        }
                                    }) {
                                        Image(systemName: Double(index) <= selectedRating ? "star.fill" : "star")
                                            .font(.title)
                                            .foregroundColor(Double(index) <= selectedRating ? .orange : .gray)
                                            .scaleEffect(Double(index) <= selectedRating ? 1.2 : 1.0)
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                }
                            }
                        }
                        
                        // Rating Description
                        Text(ratingDescription)
                            .font(.custom("Times New Roman", size: 16))
                            .fontWeight(.semibold)
                            .foregroundColor(.black)
                            .multilineTextAlignment(.center)
                            .padding()
                            .background(
                                Rectangle()
                                    .stroke(Color.black, lineWidth: 1)
                                    .background(Color.white.opacity(0.8))
                            )
                    }
                    
                    // Quick Rating Buttons
                    VStack(spacing: 15) {
                        Text("QUICK RATINGS")
                            .font(.custom("Times New Roman", size: 16))
                            .fontWeight(.bold)
                            .foregroundColor(.black)
                        
                        HStack(spacing: 20) {
                            quickRatingButton(title: "TERRIBLE", rating: 1, color: .red)
                            quickRatingButton(title: "BAD", rating: 3, color: .orange)
                            quickRatingButton(title: "OKAY", rating: 5, color: .yellow)
                            quickRatingButton(title: "GOOD", rating: 7, color: .green)
                            quickRatingButton(title: "EXCELLENT", rating: 10, color: .blue)
                        }
                    }
                    
                    Spacer()
                    
                    // Action Buttons
                    HStack(spacing: 20) {
                        Button("CANCEL") {
                            dismiss()
                        }
                        .font(.custom("Times New Roman", size: 16))
                        .fontWeight(.bold)
                        .foregroundColor(.black)
                        .padding()
                        .background(
                            Rectangle()
                                .stroke(Color.black, lineWidth: 2)
                                .background(Color.white.opacity(0.8))
                        )
                        
                        Button("SUBMIT RATING") {
                            onRatingSelected(selectedRating)
                            dismiss()
                        }
                        .font(.custom("Times New Roman", size: 16))
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding()
                        .background(Color.black)
                        .disabled(selectedRating == 0)
                        .opacity(selectedRating == 0 ? 0.6 : 1.0)
                    }
                    .padding(.bottom, 30)
                }
                .padding()
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            selectedRating = currentRating
        }
    }
    
    // MARK: - Quick Rating Button
    private func quickRatingButton(title: String, rating: Double, color: Color) -> some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.2)) {
                selectedRating = rating
            }
        }) {
            VStack(spacing: 4) {
                Text(title)
                    .font(.custom("Times New Roman", size: 12))
                    .fontWeight(.bold)
                    .foregroundColor(.black)
                
                Text("\(Int(rating))")
                    .font(.custom("Times New Roman", size: 16))
                    .fontWeight(.bold)
                    .foregroundColor(selectedRating == rating ? .white : color)
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 12)
            .background(
                Rectangle()
                    .fill(selectedRating == rating ? color : Color.white.opacity(0.8))
                    .overlay(
                        Rectangle()
                            .stroke(color, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Rating Description
    private var ratingDescription: String {
        switch selectedRating {
        case 0:
            return "Select a rating above"
        case 1...2:
            return "TERRIBLE\nNot worth your time"
        case 3...4:
            return "BAD\nHas significant flaws"
        case 5...6:
            return "OKAY\nWatchable but forgettable"
        case 7...8:
            return "GOOD\nWell made and entertaining"
        case 9...10:
            return "EXCELLENT\nOutstanding entertainment"
        default:
            return ""
        }
    }
}

#Preview {
    RetroRatingSheetView(currentRating: 7.0) { rating in
        print("Selected rating: \(rating)")
    }
}
