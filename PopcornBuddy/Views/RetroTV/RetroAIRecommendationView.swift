import SwiftUI

struct RetroAIRecommendationView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var isGenerating = false
    @State private var currentRecommendation: String = "The Batman (2022)"
    @State private var recommendationType: String = "Movie"
    @State private var aiReason: String = "Based on your love for dark, atmospheric superhero films and high ratings for Christopher Nolan movies."
    
    var body: some View {
        NavigationView {
            ZStack {
                // Retro background
                LinearGradient(
                    colors: [
                        Color(red: 0.1, green: 0.05, blue: 0.02),
                        Color(red: 0.05, green: 0.03, blue: 0.01)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: 30) {
                    // Header
                    VStack(spacing: 10) {
                        Text("AI PRINTER")
                            .font(.custom("Courier", size: 24))
                            .fontWeight(.bold)
                            .foregroundColor(.orange)
                        
                        Text("RECOMMENDATION SYSTEM")
                            .font(.custom("Courier", size: 12))
                            .foregroundColor(.gray)
                    }
                    
                    // Printer Animation
                    printerAnimation
                    
                    // Recommendation Paper
                    if !isGenerating {
                        recommendationPaper
                    }
                    
                    Spacer()
                    
                    // Action Buttons
                    VStack(spacing: 15) {
                        Button(action: generateNewRecommendation) {
                            HStack {
                                if isGenerating {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                        .progressViewStyle(CircularProgressViewStyle(tint: .black))
                                } else {
                                    Image(systemName: "printer")
                                        .font(.system(size: 16, weight: .bold))
                                }
                                
                                Text(isGenerating ? "PRINTING..." : "PRINT NEW")
                                    .font(.custom("Courier", size: 14))
                                    .fontWeight(.bold)
                            }
                            .foregroundColor(.black)
                            .padding(.horizontal, 25)
                            .padding(.vertical, 12)
                            .background(Color.orange)
                            .cornerRadius(0)
                        }
                        .disabled(isGenerating)
                        
                        Button(action: { dismiss() }) {
                            Text("CLOSE PRINTER")
                                .font(.custom("Courier", size: 14))
                                .fontWeight(.bold)
                                .foregroundColor(.orange)
                                .padding(.horizontal, 25)
                                .padding(.vertical, 12)
                                .overlay(
                                    Rectangle()
                                        .stroke(Color.orange, lineWidth: 1)
                                )
                        }
                    }
                    .padding(.bottom, 30)
                }
                .padding()
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            // Simulate initial printing
            generateNewRecommendation()
        }
    }
    
    // MARK: - Printer Animation
    private var printerAnimation: some View {
        VStack(spacing: 10) {
            // Printer Body
            RoundedRectangle(cornerRadius: 12)
                .fill(
                    LinearGradient(
                        colors: [
                            Color(red: 0.9, green: 0.9, blue: 0.9),
                            Color(red: 0.7, green: 0.7, blue: 0.7)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 200, height: 120)
                .overlay(
                    VStack(spacing: 8) {
                        // Status Light
                        HStack {
                            Spacer()
                            Circle()
                                .fill(isGenerating ? Color.orange : Color.green)
                                .frame(width: 12, height: 12)
                                .scaleEffect(isGenerating ? 1.2 : 1.0)
                                .animation(.easeInOut(duration: 0.5).repeatForever(autoreverses: true), value: isGenerating)
                        }
                        .padding(.horizontal)
                        
                        // Paper Tray
                        Rectangle()
                            .fill(Color.white)
                            .frame(width: 160, height: 15)
                            .cornerRadius(3)
                        
                        // AI Label
                        Text("AI POWERED")
                            .font(.custom("Courier", size: 8))
                            .fontWeight(.bold)
                            .foregroundColor(.black)
                    }
                    .padding()
                )
                .shadow(color: .black.opacity(0.5), radius: 15, x: 0, y: 8)
            
            // Status Text
            Text(isGenerating ? "ANALYZING YOUR PREFERENCES..." : "READY TO PRINT")
                .font(.custom("Courier", size: 10))
                .foregroundColor(isGenerating ? .orange : .green)
                .fontWeight(.bold)
        }
    }
    
    // MARK: - Recommendation Paper
    private var recommendationPaper: some View {
        VStack(spacing: 0) {
            // Paper
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.white)
                .frame(width: 250, height: 200)
                .overlay(
                    VStack(alignment: .leading, spacing: 12) {
                        // Header
                        HStack {
                            Text("🤖 AI RECOMMENDATION")
                                .font(.custom("Courier", size: 12))
                                .fontWeight(.bold)
                                .foregroundColor(.black)
                            
                            Spacer()
                            
                            Text(Date().formatted(.dateTime.day().month().year()))
                                .font(.custom("Courier", size: 8))
                                .foregroundColor(.gray)
                        }
                        
                        Divider()
                            .background(Color.black)
                        
                        // Recommendation
                        VStack(alignment: .leading, spacing: 8) {
                            Text("RECOMMENDED \(recommendationType.uppercased()):")
                                .font(.custom("Courier", size: 10))
                                .fontWeight(.bold)
                                .foregroundColor(.black)
                            
                            Text(currentRecommendation)
                                .font(.custom("Courier", size: 14))
                                .fontWeight(.bold)
                                .foregroundColor(.orange)
                            
                            Text("WHY THIS PICK:")
                                .font(.custom("Courier", size: 10))
                                .fontWeight(.bold)
                                .foregroundColor(.black)
                                .padding(.top, 5)
                            
                            Text(aiReason)
                                .font(.custom("Courier", size: 9))
                                .foregroundColor(.black)
                                .lineLimit(4)
                        }
                        
                        Spacer()
                        
                        // Footer
                        HStack {
                            Text("CONFIDENCE: 94%")
                                .font(.custom("Courier", size: 8))
                                .foregroundColor(.green)
                            
                            Spacer()
                            
                            Text("POPCORN BUDDY AI")
                                .font(.custom("Courier", size: 8))
                                .foregroundColor(.gray)
                        }
                    }
                    .padding()
                )
                .shadow(color: .black.opacity(0.3), radius: 8, x: 2, y: 4)
                .rotation3DEffect(
                    .degrees(2),
                    axis: (x: 1, y: 0, z: 0)
                )
        }
    }
    
    // MARK: - Generate New Recommendation
    private func generateNewRecommendation() {
        isGenerating = true
        
        // Simulate AI processing
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            // Sample recommendations
            let recommendations = [
                ("Dune: Part Two (2024)", "Movie", "Perfect for fans of epic sci-fi with stunning visuals and complex storytelling."),
                ("The Bear", "TV Show", "Based on your appreciation for character-driven dramas with authentic dialogue."),
                ("Oppenheimer (2023)", "Movie", "Recommended for viewers who enjoy biographical dramas and Christopher Nolan's direction."),
                ("Wednesday", "TV Show", "Ideal for those who love dark comedy and supernatural mystery elements."),
                ("Everything Everywhere All at Once", "Movie", "Perfect match for your taste in innovative, genre-bending cinema.")
            ]
            
            let randomRec = recommendations.randomElement()!
            currentRecommendation = randomRec.0
            recommendationType = randomRec.1
            aiReason = randomRec.2
            
            isGenerating = false
        }
    }
}

#Preview {
    RetroAIRecommendationView()
}
