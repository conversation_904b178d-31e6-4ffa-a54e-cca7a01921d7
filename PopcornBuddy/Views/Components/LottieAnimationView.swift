import SwiftUI
// import Lottie // Uncomment when <PERSON><PERSON> is added to the project

// MARK: - <PERSON>tie Animation View Wrapper
struct LottieAnimationView: View {
    let animationName: String
    let loopMode: LottieLoopMode
    let speed: Double
    
    init(
        animationName: String,
        loopMode: LottieLoopMode = .loop,
        speed: Double = 1.0
    ) {
        self.animationName = animationName
        self.loopMode = loopMode
        self.speed = speed
    }
    
    var body: some View {
        // Placeholder until <PERSON><PERSON> is added
        // Once <PERSON><PERSON> is added, replace this with:
        // LottieView(animation: .named(animationName))
        //     .looping(loopMode == .loop)
        //     .animationSpeed(speed)
        
        // Temporary placeholder with system animation
        Image(systemName: "tv.fill")
            .font(.system(size: 50))
            .foregroundColor(.orange)
            .scaleEffect(1.0)
            .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true), value: true)
    }
}

// MARK: - Lottie Loop Mode
enum LottieLoopMode {
    case playOnce
    case loop
    case autoReverse
}

// MARK: - Retro TV Static Animation
struct RetroTVStaticView: View {
    @State private var staticOpacity: Double = 0.1
    @State private var staticOffset: CGFloat = 0
    
    var body: some View {
        ZStack {
            // Base static pattern
            Rectangle()
                .fill(
                    LinearGradient(
                        colors: [
                            Color.white.opacity(staticOpacity),
                            Color.gray.opacity(staticOpacity * 0.5),
                            Color.black.opacity(staticOpacity * 0.3)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            // Moving static lines
            VStack(spacing: 1) {
                ForEach(0..<50, id: \.self) { _ in
                    Rectangle()
                        .fill(Color.white.opacity(Double.random(in: 0.1...0.3)))
                        .frame(height: 2)
                        .offset(x: staticOffset)
                }
            }
        }
        .onAppear {
            withAnimation(.linear(duration: 0.1).repeatForever(autoreverses: false)) {
                staticOpacity = 0.3
            }
            
            withAnimation(.linear(duration: 0.05).repeatForever(autoreverses: true)) {
                staticOffset = CGFloat.random(in: -5...5)
            }
        }
    }
}

// MARK: - Retro Channel Switch Animation
struct RetroChannelSwitchView: View {
    @State private var isAnimating = false
    
    var body: some View {
        ZStack {
            Rectangle()
                .fill(Color.black)
            
            if isAnimating {
                RetroTVStaticView()
                    .opacity(0.8)
            }
            
            // Channel switching text
            VStack {
                Text("SWITCHING CHANNEL...")
                    .font(.custom("Courier", size: 16))
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                    .opacity(isAnimating ? 1 : 0)
                
                HStack(spacing: 2) {
                    ForEach(0..<3, id: \.self) { index in
                        Circle()
                            .fill(Color.orange)
                            .frame(width: 8, height: 8)
                            .scaleEffect(isAnimating ? 1.0 : 0.5)
                            .animation(
                                .easeInOut(duration: 0.6)
                                .repeatForever(autoreverses: true)
                                .delay(Double(index) * 0.2),
                                value: isAnimating
                            )
                    }
                }
            }
        }
        .onAppear {
            isAnimating = true
        }
    }
}

// MARK: - Retro Loading Animation
struct RetroLoadingView: View {
    @State private var rotationAngle: Double = 0
    @State private var scale: Double = 1.0
    
    var body: some View {
        VStack(spacing: 20) {
            // Spinning TV icon
            Image(systemName: "tv.fill")
                .font(.system(size: 40))
                .foregroundColor(.orange)
                .rotationEffect(.degrees(rotationAngle))
                .scaleEffect(scale)
                .onAppear {
                    withAnimation(.linear(duration: 2.0).repeatForever(autoreverses: false)) {
                        rotationAngle = 360
                    }
                    
                    withAnimation(.easeInOut(duration: 1.0).repeatForever(autoreverses: true)) {
                        scale = 1.2
                    }
                }
            
            Text("LOADING...")
                .font(.custom("Courier", size: 14))
                .fontWeight(.bold)
                .foregroundColor(.orange)
        }
    }
}

// MARK: - Retro Button Press Animation
struct RetroButtonPressView: View {
    @State private var isPressed = false
    let action: () -> Void
    let content: AnyView
    
    init<Content: View>(action: @escaping () -> Void, @ViewBuilder content: () -> Content) {
        self.action = action
        self.content = AnyView(content())
    }
    
    var body: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
                action()
            }
        }) {
            content
                .scaleEffect(isPressed ? 0.95 : 1.0)
                .brightness(isPressed ? -0.2 : 0)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Retro Glow Effect
struct RetroGlowEffect: ViewModifier {
    let color: Color
    let radius: CGFloat
    
    func body(content: Content) -> some View {
        content
            .shadow(color: color.opacity(0.6), radius: radius, x: 0, y: 0)
            .shadow(color: color.opacity(0.3), radius: radius * 2, x: 0, y: 0)
    }
}

extension View {
    func retroGlow(color: Color = .orange, radius: CGFloat = 5) -> some View {
        modifier(RetroGlowEffect(color: color, radius: radius))
    }
}

#Preview {
    VStack(spacing: 30) {
        LottieAnimationView(animationName: "tv_static")
            .frame(width: 100, height: 100)
        
        RetroTVStaticView()
            .frame(width: 200, height: 150)
            .clipShape(RoundedRectangle(cornerRadius: 10))
        
        RetroChannelSwitchView()
            .frame(width: 200, height: 100)
            .clipShape(RoundedRectangle(cornerRadius: 10))
        
        RetroLoadingView()
    }
    .padding()
    .background(Color.black)
}
