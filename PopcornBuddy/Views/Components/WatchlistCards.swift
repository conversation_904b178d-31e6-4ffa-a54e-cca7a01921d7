import SwiftUI
import CoreData

// MARK: - Watchlist Movie Card
struct WatchlistMovieCard: View {
    let movie: Movie
    
    var body: some View {
        NavigationLink(destination: DetailView(mediaItem: movie.toMediaItem())) {
            VStack(alignment: .leading, spacing: 8) {
                // Poster Image
                AsyncImage(url: posterURL) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.3))
                        .overlay(
                            ProgressView()
                                .scaleEffect(0.8)
                        )
                }
                .frame(width: 120, height: 180)
                .clipShape(RoundedRectangle(cornerRadius: 12))

                // Title and Year
                VStack(alignment: .leading, spacing: 2) {
                    Text(movie.title ?? "Unknown Title")
                        .font(.caption)
                        .fontWeight(.medium)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                        .frame(height: 32, alignment: .top) // Fixed height for title

                    HStack {
                        Text(yearString)
                            .font(.caption2)
                            .foregroundColor(.secondary)

                        Spacer()

                        // User Rating or TMDB Rating
                        HStack(spacing: 2) {
                            Image(systemName: movie.userRating > 0 ? "star.fill" : "star")
                                .font(.caption2)
                                .foregroundColor(movie.userRating > 0 ? .yellow : .gray)
                            Text(ratingString)
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .frame(height: 50) // Fixed height for text area
            }
            .frame(width: 120, height: 238) // Fixed total height
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var posterURL: URL? {
        guard let posterPath = movie.posterPath else { return nil }
        return TMDBService.shared.posterURL(for: posterPath, size: .w500)
    }
    
    private var yearString: String {
        guard let releaseDate = movie.releaseDate else { return "TBA" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy"
        return formatter.string(from: releaseDate)
    }
    
    private var ratingString: String {
        if movie.userRating > 0 {
            return String(format: "%.1f", movie.userRating)
        } else {
            return String(format: "%.1f", movie.voteAverage)
        }
    }
}

// MARK: - Watchlist TV Show Card
struct WatchlistTVShowCard: View {
    let tvShow: TVShow
    
    var body: some View {
        NavigationLink(destination: DetailView(mediaItem: tvShow.toMediaItem())) {
            VStack(alignment: .leading, spacing: 8) {
                // Poster Image
                AsyncImage(url: posterURL) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.gray.opacity(0.3))
                        .overlay(
                            ProgressView()
                                .scaleEffect(0.8)
                        )
                }
                .frame(width: 120, height: 180)
                .clipShape(RoundedRectangle(cornerRadius: 12))

                // Title and Year
                VStack(alignment: .leading, spacing: 2) {
                    Text(tvShow.name ?? "Unknown Title")
                        .font(.caption)
                        .fontWeight(.medium)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                        .frame(height: 32, alignment: .top) // Fixed height for title

                    HStack {
                        Text(yearString)
                            .font(.caption2)
                            .foregroundColor(.secondary)

                        Spacer()

                        // User Rating or TMDB Rating
                        HStack(spacing: 2) {
                            Image(systemName: tvShow.userRating > 0 ? "star.fill" : "star")
                                .font(.caption2)
                                .foregroundColor(tvShow.userRating > 0 ? .yellow : .gray)
                            Text(ratingString)
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }
                .frame(height: 50) // Fixed height for text area
            }
            .frame(width: 120, height: 238) // Fixed total height
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var posterURL: URL? {
        guard let posterPath = tvShow.posterPath else { return nil }
        return TMDBService.shared.posterURL(for: posterPath, size: .w500)
    }
    
    private var yearString: String {
        guard let firstAirDate = tvShow.firstAirDate else { return "TBA" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy"
        return formatter.string(from: firstAirDate)
    }
    
    private var ratingString: String {
        if tvShow.userRating > 0 {
            return String(format: "%.1f", tvShow.userRating)
        } else {
            return String(format: "%.1f", tvShow.voteAverage)
        }
    }
}

// MARK: - Core Data Extensions
extension Movie {
    func toMediaItem() -> MediaItem {
        return MediaItem(
            id: Int(self.id),
            title: self.title ?? "Unknown Title",
            overview: self.overview ?? "",
            posterURL: posterURL,
            backdropURL: backdropURL,
            rating: self.voteAverage,
            year: yearString,
            runtime: self.runtime > 0 ? Int(self.runtime) : nil
        )
    }
    
    private var posterURL: URL? {
        guard let posterPath = self.posterPath else { return nil }
        return TMDBService.shared.posterURL(for: posterPath, size: .w500)
    }
    
    private var backdropURL: URL? {
        guard let backdropPath = self.backdropPath else { return nil }
        return TMDBService.shared.backdropURL(for: backdropPath, size: .w1280)
    }
    
    private var yearString: String? {
        guard let releaseDate = self.releaseDate else { return nil }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy"
        return formatter.string(from: releaseDate)
    }
}

extension TVShow {
    func toMediaItem() -> MediaItem {
        return MediaItem(
            id: Int(self.id),
            title: self.name ?? "Unknown Title",
            overview: self.overview ?? "",
            posterURL: posterURL,
            backdropURL: backdropURL,
            rating: self.voteAverage,
            year: yearString,
            runtime: nil // TV shows don't have a single runtime
        )
    }
    
    private var posterURL: URL? {
        guard let posterPath = self.posterPath else { return nil }
        return TMDBService.shared.posterURL(for: posterPath, size: .w500)
    }
    
    private var backdropURL: URL? {
        guard let backdropPath = self.backdropPath else { return nil }
        return TMDBService.shared.backdropURL(for: backdropPath, size: .w1280)
    }
    
    private var yearString: String? {
        guard let firstAirDate = self.firstAirDate else { return nil }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy"
        return formatter.string(from: firstAirDate)
    }
}
