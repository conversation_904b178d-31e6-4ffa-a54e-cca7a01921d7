import SwiftUI

// MARK: - Retro Color Palette
struct RetroColors {
    static let tvBrown = Color(red: 0.4, green: 0.3, blue: 0.2)
    static let tvDarkBrown = Color(red: 0.3, green: 0.2, blue: 0.1)
    static let tvScreen = Color.black
    static let amber = Color(red: 1.0, green: 0.75, blue: 0.0)
    static let retroOrange = Color(red: 1.0, green: 0.6, blue: 0.2)
    static let retroGreen = Color(red: 0.2, green: 0.8, blue: 0.2)
    static let retroRed = Color(red: 0.9, green: 0.2, blue: 0.2)
    static let paperWhite = Color(red: 0.95, green: 0.92, blue: 0.85)
    static let inkBlack = Color(red: 0.1, green: 0.1, blue: 0.1)
}

// MARK: - Retro Fonts
struct RetroFonts {
    static func courier(size: CGFloat) -> Font {
        .custom("Courier", size: size)
    }
    
    static func timesNewRoman(size: CGFloat) -> Font {
        .custom("Times New Roman", size: size)
    }
    
    static func system(size: CGFloat, weight: Font.Weight = .regular) -> Font {
        .system(size: size, weight: weight, design: .monospaced)
    }
}

// MARK: - Retro TV Frame
struct RetroTVFrame<Content: View>: View {
    let content: Content
    let width: CGFloat
    let height: CGFloat
    
    init(width: CGFloat, height: CGFloat, @ViewBuilder content: () -> Content) {
        self.width = width
        self.height = height
        self.content = content()
    }
    
    var body: some View {
        ZStack {
            // TV Frame
            RoundedRectangle(cornerRadius: 25)
                .fill(
                    LinearGradient(
                        colors: [
                            RetroColors.tvBrown,
                            RetroColors.tvDarkBrown,
                            Color(red: 0.2, green: 0.15, blue: 0.1)
                        ],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: width, height: height)
                .shadow(color: .black.opacity(0.5), radius: 20, x: 0, y: 10)
            
            // Screen Bezel
            RoundedRectangle(cornerRadius: 15)
                .fill(Color.black)
                .frame(width: width * 0.85, height: height * 0.8)
                .overlay(
                    RoundedRectangle(cornerRadius: 15)
                        .fill(
                            RadialGradient(
                                colors: [
                                    Color.white.opacity(0.1),
                                    Color.clear
                                ],
                                center: .center,
                                startRadius: 0,
                                endRadius: 200
                            )
                        )
                )
            
            // Content
            content
                .frame(width: width * 0.8, height: height * 0.75)
                .clipShape(RoundedRectangle(cornerRadius: 12))
        }
    }
}

// MARK: - Retro Button
struct RetroButton: View {
    let title: String
    let icon: String?
    let action: () -> Void
    let style: RetroButtonStyle
    
    enum RetroButtonStyle {
        case primary
        case secondary
        case danger
        case success
        
        var backgroundColor: Color {
            switch self {
            case .primary: return RetroColors.retroOrange
            case .secondary: return Color.white.opacity(0.1)
            case .danger: return RetroColors.retroRed
            case .success: return RetroColors.retroGreen
            }
        }
        
        var foregroundColor: Color {
            switch self {
            case .primary: return .black
            case .secondary: return .white
            case .danger: return .white
            case .success: return .black
            }
        }
        
        var borderColor: Color {
            switch self {
            case .primary: return RetroColors.retroOrange
            case .secondary: return RetroColors.retroOrange.opacity(0.3)
            case .danger: return RetroColors.retroRed
            case .success: return RetroColors.retroGreen
            }
        }
    }
    
    init(title: String, icon: String? = nil, style: RetroButtonStyle = .primary, action: @escaping () -> Void) {
        self.title = title
        self.icon = icon
        self.style = style
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                if let icon = icon {
                    Image(systemName: icon)
                        .font(.system(size: 16, weight: .bold))
                }
                
                Text(title)
                    .font(RetroFonts.courier(size: 14))
                    .fontWeight(.bold)
            }
            .foregroundColor(style.foregroundColor)
            .padding(.horizontal, 20)
            .padding(.vertical, 12)
            .background(
                Rectangle()
                    .fill(style.backgroundColor)
                    .overlay(
                        Rectangle()
                            .stroke(style.borderColor, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(RetroButtonPressStyle())
    }
}

// MARK: - Retro Card
struct RetroCard<Content: View>: View {
    let content: Content
    let style: CardStyle
    
    enum CardStyle {
        case tv
        case newspaper
        case control
        
        var backgroundColor: Color {
            switch self {
            case .tv: return Color.white.opacity(0.1)
            case .newspaper: return RetroColors.paperWhite
            case .control: return Color.black.opacity(0.3)
            }
        }
        
        var borderColor: Color {
            switch self {
            case .tv: return RetroColors.retroOrange.opacity(0.3)
            case .newspaper: return RetroColors.inkBlack
            case .control: return RetroColors.retroOrange.opacity(0.5)
            }
        }
    }
    
    init(style: CardStyle = .tv, @ViewBuilder content: () -> Content) {
        self.style = style
        self.content = content()
    }
    
    var body: some View {
        content
            .padding()
            .background(
                RoundedRectangle(cornerRadius: style == .newspaper ? 0 : 10)
                    .fill(style.backgroundColor)
                    .overlay(
                        RoundedRectangle(cornerRadius: style == .newspaper ? 0 : 10)
                            .stroke(style.borderColor, lineWidth: style == .newspaper ? 2 : 1)
                    )
            )
    }
}

// MARK: - Retro Text Field
struct RetroTextField: View {
    let title: String
    @Binding var text: String
    let placeholder: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title.uppercased())
                .font(RetroFonts.courier(size: 12))
                .fontWeight(.bold)
                .foregroundColor(RetroColors.retroOrange)
            
            TextField(placeholder, text: $text)
                .font(RetroFonts.courier(size: 14))
                .foregroundColor(.white)
                .padding()
                .background(
                    Rectangle()
                        .fill(Color.black.opacity(0.3))
                        .overlay(
                            Rectangle()
                                .stroke(RetroColors.retroOrange.opacity(0.5), lineWidth: 1)
                        )
                )
        }
    }
}

// MARK: - Retro Progress Bar
struct RetroProgressBar: View {
    let progress: Double
    let color: Color
    
    init(progress: Double, color: Color = RetroColors.retroOrange) {
        self.progress = progress
        self.color = color
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                Rectangle()
                    .fill(Color.black.opacity(0.3))
                    .frame(height: 8)
                
                Rectangle()
                    .fill(color)
                    .frame(width: geometry.size.width * progress, height: 8)
                    .animation(.easeInOut(duration: 0.5), value: progress)
            }
        }
        .frame(height: 8)
        .overlay(
            Rectangle()
                .stroke(color.opacity(0.5), lineWidth: 1)
        )
    }
}

// MARK: - Retro Toggle
struct RetroToggle: View {
    let title: String
    @Binding var isOn: Bool
    
    var body: some View {
        HStack {
            Text(title.uppercased())
                .font(RetroFonts.courier(size: 14))
                .fontWeight(.bold)
                .foregroundColor(.white)
            
            Spacer()
            
            Button(action: {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isOn.toggle()
                }
            }) {
                ZStack {
                    Rectangle()
                        .fill(isOn ? RetroColors.retroGreen : Color.gray.opacity(0.3))
                        .frame(width: 50, height: 25)
                        .overlay(
                            Rectangle()
                                .stroke(Color.white.opacity(0.5), lineWidth: 1)
                        )
                    
                    Circle()
                        .fill(Color.white)
                        .frame(width: 20, height: 20)
                        .offset(x: isOn ? 12 : -12)
                        .animation(.easeInOut(duration: 0.2), value: isOn)
                }
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
}

// MARK: - Retro Button Press Style
struct RetroButtonPressStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .brightness(configuration.isPressed ? -0.1 : 0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - Retro Divider
struct RetroDivider: View {
    let color: Color
    let thickness: CGFloat
    
    init(color: Color = RetroColors.retroOrange, thickness: CGFloat = 1) {
        self.color = color
        self.thickness = thickness
    }
    
    var body: some View {
        Rectangle()
            .fill(color)
            .frame(height: thickness)
            .opacity(0.6)
    }
}

#Preview {
    VStack(spacing: 20) {
        RetroTVFrame(width: 300, height: 200) {
            Text("RETRO TV")
                .font(RetroFonts.courier(size: 20))
                .foregroundColor(RetroColors.retroOrange)
        }
        
        RetroButton(title: "PLAY", icon: "play.fill") {}
        
        RetroCard(style: .tv) {
            Text("TV Style Card")
                .font(RetroFonts.courier(size: 16))
                .foregroundColor(.white)
        }
        
        RetroProgressBar(progress: 0.7)
            .frame(width: 200)
    }
    .padding()
    .background(Color.black)
}
