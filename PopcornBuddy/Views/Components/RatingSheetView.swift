import SwiftUI

struct RatingSheetView: View {
    let currentRating: Double
    let onRatingSelected: (Double) -> Void
    
    @State private var selectedRating: Double = 0
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Header
                VStack(spacing: 8) {
                    Text("Rate this content")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    Text("Tap a star to rate")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .padding(.top, 20)
                
                // Star Rating
                HStack(spacing: 8) {
                    ForEach(1...10, id: \.self) { index in
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                selectedRating = Double(index)
                            }
                        }) {
                            Image(systemName: Double(index) <= selectedRating ? "star.fill" : "star")
                                .font(.title)
                                .foregroundColor(Double(index) <= selectedRating ? .yellow : .gray)
                                .scaleEffect(Double(index) <= selectedRating ? 1.1 : 1.0)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal)
                
                // Rating Text
                if selectedRating > 0 {
                    Text("\(String(format: "%.0f", selectedRating))/10")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                        .transition(.opacity.combined(with: .scale))
                }
                
                Spacer()
                
                // Action Buttons
                VStack(spacing: 16) {
                    // Submit Rating Button
                    Button(action: {
                        onRatingSelected(selectedRating)
                    }) {
                        Text("Submit Rating")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 16)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(selectedRating > 0 ? Color.blue : Color.gray)
                            )
                    }
                    .disabled(selectedRating == 0)
                    .padding(.horizontal)
                    
                    // Remove Rating Button (if there's an existing rating)
                    if currentRating > 0 {
                        Button(action: {
                            onRatingSelected(0)
                        }) {
                            Text("Remove Rating")
                                .font(.subheadline)
                                .foregroundColor(.red)
                        }
                    }
                }
                .padding(.bottom, 30)
            }
            .navigationTitle("Rate Content")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            selectedRating = currentRating
        }
    }
}

#Preview {
    RatingSheetView(currentRating: 7.0) { rating in
        print("Selected rating: \(rating)")
    }
}
